package com.jdh.o2oservice.web.controller.admin.provider;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.application.provider.service.ProviderApplication;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.JdhStoreTransferStationTypeEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelStationPageQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.StationDbQuery;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStation;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationRel;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhStoreTransferStationRelRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhStoreTransferStationRepository;
import com.jdh.o2oservice.export.laboratory.cmd.AddQuickMerchantStoreRequest;
import com.jdh.o2oservice.export.laboratory.cmd.AppointmentMigrationRequest;
import com.jdh.o2oservice.export.laboratory.cmd.UpdateQuickMerchantStoreRequest;
import com.jdh.o2oservice.export.laboratory.dto.JdhStoreTransferStationDto;
import com.jdh.o2oservice.export.laboratory.dto.QueryMedicalPromisePageResponse;
import com.jdh.o2oservice.export.laboratory.dto.QueryMerchantStoreDetailResponse;
import com.jdh.o2oservice.export.laboratory.query.QueryMedicalPromisePageRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreDetailByParamRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreListByParamRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryProvidersRequest;
import com.jdh.o2oservice.export.provider.dto.ProviderDto;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.provider.dto.StationAddressDto;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.StationAddressRequest;
import com.jdh.o2oservice.export.provider.query.StoreInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商家门店
 *
 * <AUTHOR>
 * @date 2024/04/23
 */
@Slf4j
@RestController
@RequestMapping("/provider/station")
public class ProviderStationController {
    
    /**
     * 接口信息
     */
    @Resource
    ProviderStoreApplication providerStoreApplication;

    @Resource
    ProviderApplication providerApplication;

    /**
     * 查询门店详情
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/query")
    @LogAndAlarm(jKey = "ProviderStationController.query")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询门店详情")
    public Response<StoreInfoDto> query(@RequestBody StoreInfoRequest request) {
        StoreInfoDto dto = providerStoreApplication.queryStationInfo(request);
        return ResponseUtil.buildSuccResponse(dto);
    }

    /**
     * 查询已开通省市
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/addressList")
    @LogAndAlarm(jKey = "ProviderStationController.addressList")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "已开通门店省市县")
    public Response<List<StationAddressDto>> addressList(@RequestBody StationAddressRequest request) {
        if (request == null) {
            request = new StationAddressRequest();
        }
        // 默认查询省份
        if (request.getMark() == null) {
             request.setMark(1);
        }
        List<StationAddressDto> dto = providerStoreApplication.queryStationAddress(request);
        return ResponseUtil.buildSuccResponse(dto);
    }

    /**
     * 查询已开通省市
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/stationListByAddress")
    @LogAndAlarm(jKey = "ProviderStationController.stationListByAddress")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询三级地址下门店列表")
    public Response<List<StoreInfoDto>> stationListByAddress(@RequestBody StationAddressRequest request) {
        List<StoreInfoDto> dto = providerStoreApplication.listStationByAddress(request);
        return ResponseUtil.buildSuccResponse(dto);
    }

    /**
     * 新建实验室
     * @return
     */
    @RequestMapping(value = "/addQuickMerchantStore", method = RequestMethod.POST)
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "新建实验室")
    public Response<Boolean> addQuickMerchantStore(@RequestBody AddQuickMerchantStoreRequest addQuickMerchantStoreRequest){
        String erp = LoginContext.getLoginContext().getPin();
        addQuickMerchantStoreRequest.setErp(erp);
        //新增
        addQuickMerchantStoreRequest.setOperateType(1);
        Boolean result = providerStoreApplication.addQuickMerchantStore(addQuickMerchantStoreRequest);
        return Response.buildSuccessResult(result);
    }


    /**
     * 修改实验室
     * @return
     */
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "更新实验室")
    @RequestMapping(value = "/updateQuickMerchantStore", method = RequestMethod.POST)
    @LogAndAlarm
    public Response<Boolean> updateQuickMerchantStore(@RequestBody UpdateQuickMerchantStoreRequest updateQuickMerchantStoreRequest){
        String erp = LoginContext.getLoginContext().getPin();
        updateQuickMerchantStoreRequest.setErp(erp);
        //编辑
        updateQuickMerchantStoreRequest.setOperateType(2);
        Boolean result = providerStoreApplication.updateQuickMerchantStore(updateQuickMerchantStoreRequest);
        return Response.buildSuccessResult(result);
    }

    /**
     * 查询实验室详情
     * @return
     */
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询实验室详情")
    @RequestMapping(value = "/queryMerchantStoreDetailByParam", method = RequestMethod.POST)
    @LogAndAlarm
    public Response<QueryMerchantStoreDetailResponse> queryMerchantStoreDetailByParam(@RequestBody QueryMerchantStoreDetailByParamRequest queryMerchantStoreDetailByParamRequest){
        String erp = LoginContext.getLoginContext().getPin();
        queryMerchantStoreDetailByParamRequest.setErp(erp);
        queryMerchantStoreDetailByParamRequest.setQueryTransferStation(true);
        QueryMerchantStoreDetailResponse queryMerchantStoreDetailResponse = providerStoreApplication.queryMerchantStoreDetailByParam(queryMerchantStoreDetailByParamRequest);
        if(queryMerchantStoreDetailResponse != null && CollUtil.isNotEmpty(queryMerchantStoreDetailResponse.getTransferStations())) {
            for (JdhStoreTransferStationDto jdhStoreTransferStationDto :queryMerchantStoreDetailResponse.getTransferStations()) {
                if (JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(jdhStoreTransferStationDto.getStationType())) {
                    jdhStoreTransferStationDto.setCanDelete(false);
                    jdhStoreTransferStationDto.setCanModify(false);
                } else {
                    jdhStoreTransferStationDto.setCanDelete(true);
                    jdhStoreTransferStationDto.setCanModify(true);
                }
            }
        }
        return Response.buildSuccessResult(queryMerchantStoreDetailResponse);
    }

    /**
     * 查询实验室列表(分页)
     * @return
     */
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询实验室列表(分页)")
    @RequestMapping(value = "/queryMerchantStoreListByParam", method = RequestMethod.POST)
    @LogAndAlarm
    public Response<PageDto<QueryMerchantStoreDetailResponse>> queryMerchantStoreListByParam(@RequestBody QueryMerchantStoreListByParamRequest queryMerchantStoreListByParamRequest){
        String erp = LoginContext.getLoginContext().getPin();
        queryMerchantStoreListByParamRequest.setErp(erp);
        PageDto<QueryMerchantStoreDetailResponse> queryMerchantStoreDetailResponse = providerStoreApplication.queryMerchantStoreListByParam(queryMerchantStoreListByParamRequest);
        return Response.buildSuccessResult(queryMerchantStoreDetailResponse);
    }


    /**
     * 查询商家列表
     * @return
     */
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询商家列表")
    @RequestMapping(value = "/getMerchatProviders", method = RequestMethod.POST)
    @LogAndAlarm
    public Response<List<ProviderDto>> getMerchatProviders(@RequestBody QueryProvidersRequest queryProvidersRequest){
        String erp = LoginContext.getLoginContext().getPin();
        queryProvidersRequest.setErp(erp);
        List<ProviderDto> providerDto = providerApplication.getMerchatProviders(queryProvidersRequest);
        return Response.buildSuccessResult(providerDto);
    }

    /**
     * 更新实验室迁移配置
     * @return
     */
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "更新实验室迁移配置")
    @RequestMapping(value = "/appointmentMigration", method = RequestMethod.POST)
    @LogAndAlarm
    public Response<Boolean> appointmentMigration(@RequestBody AppointmentMigrationRequest appointmentMigrationRequest){
        String erp = LoginContext.getLoginContext().getPin();
        appointmentMigrationRequest.setErp(erp);
        Boolean result = providerStoreApplication.appointmentMigration(appointmentMigrationRequest);
        return Response.buildSuccessResult(result);
    }

    /**
     * 查询检测单列表
     * @return
     */
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询检测单列表")
    @RequestMapping(value = "/queryMedicalPromisePage", method = RequestMethod.POST)
    @LogAndAlarm
    public Response<PageDto<QueryMedicalPromisePageResponse>> queryMedicalPromisePage(@RequestBody QueryMedicalPromisePageRequest queryMedicalPromisePageRequest){
        String erp = LoginContext.getLoginContext().getPin();
        queryMedicalPromisePageRequest.setErp(erp);
        PageDto<QueryMedicalPromisePageResponse> page = providerStoreApplication.queryMedicalPromisePage(queryMedicalPromisePageRequest);
        return Response.buildSuccessResult(page);
    }
}
