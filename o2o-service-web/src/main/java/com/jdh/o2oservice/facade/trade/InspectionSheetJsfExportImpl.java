package com.jdh.o2oservice.facade.trade;

import com.alibaba.fastjson.JSONObject;
import com.jd.fastjson.JSON;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.trade.service.InspectionSheetApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.PartnerSourceEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.vo.JdOrderExtendVo;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.InspectProjectChildDetailDTO;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.trade.InspectionSheetJsfExport;
import com.jdh.o2oservice.export.trade.dto.InspectionSheetButtonDTO;
import com.jdh.o2oservice.export.trade.dto.QueryMedicalPromiseDTO;
import com.jdh.o2oservice.export.trade.dto.QueryMedicalPromisePatientDTO;
import com.jdh.o2oservice.export.trade.dto.QueryOrderDTO;
import com.jdh.o2oservice.export.trade.query.InspectionSheetButtonParam;
import com.jdh.o2oservice.export.trade.query.QueryMedicalPromiseInfoRequest;
import com.jdh.o2oservice.export.trade.query.QueryOrderInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.collect;

/**
 * @ClassName PromiseJsfExportFacadeImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/23 14:53
 **/
@Slf4j
@Service
public class InspectionSheetJsfExportImpl implements InspectionSheetJsfExport {

    /**
     * tradeApplication
     */
    @Resource
    private InspectionSheetApplication inspectionSheetApplication;

    @Resource
    private JdOrderRepository jdOrderRepository;

    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    @Resource
    private PromiseApplication promiseApplication;

    @Value("${h5SettleUrl}")
    private String h5SettleUrl;
    @Value("${orderDetailUrl}")
    private String orderDetailUrl;


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.InspectionSheetJsfExportImpl.queryInspectionSheetButton")
    public Response<InspectionSheetButtonDTO> queryInspectionSheetButton(InspectionSheetButtonParam param) {
        String userPin = "";
        try {
            AssertUtils.nonNull(param, "param is not null");
            AssertUtils.hasText(param.getUserPin(), "userPin is not null");
            if (Objects.isNull(param.getSheetId()) && StringUtils.isBlank(param.getFullAddress())) {
                throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
            }
            InspectionSheetButtonDTO result = inspectionSheetApplication.queryInspectionSheetButton(param);
            log.info("InspectionSheetJsfExportImpl queryInspectionSheetButton 查询是否可以下检验单 userPin:{} param:{} result:{}",
                    userPin, JSONObject.toJSONString(result), JSONObject.toJSONString(result));
            return Response.buildSuccessResult(result);
        } catch (BusinessException e) {
            log.error("InspectionSheetJsfExportImpl queryInspectionSheetButton 查询是否可以下检验单 business error userPin:{} param:{} ", userPin,
                    JSONObject.toJSONString(param), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("InspectionSheetJsfExportImpl queryInspectionSheetButton 查询是否可以下检验单 unknown error userPin:{} param:{}", userPin,
                    JSONObject.toJSONString(param), e);
            return Response.buildUnknownErrorResult();
        }
    }

    @Override
    public List<InspectProjectChildDetailDTO> test(InspectionSheetButtonParam param) {
        return inspectionSheetApplication.querySettlementProjectChild(param);
    }

    @Override
    @LogAndAlarm
    public Response<QueryOrderDTO> queryOrderInfo(QueryOrderInfoRequest request) {
        AssertUtils.nonNull(request, "request is null");
        AssertUtils.nonNull(request.getPartnerSource(), "request PartnerSource is null");
        AssertUtils.nonNull(request.getPartnerSourceOrderId(), "request PartnerSourceOrderId is null");
        QueryOrderDTO queryOrderDTO = QueryOrderDTO.builder()
                .partnerSourceOrderId(request.getPartnerSourceOrderId())
                .build();

        JdOrder jdOrder = JdOrder.builder().partnerSourceOrderId(request.getPartnerSourceOrderId()).partnerSource(request.getPartnerSource()).build();
        List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByPartnerSource(jdOrder);
        if (StringUtils.isNotBlank(request.getSaleChannelId())) {
            jdOrderList = jdOrderList.stream().filter(s -> request.getSaleChannelId().equalsIgnoreCase(JSON.parseObject(s.getExtend(), JdOrderExtendVo.class).getSaleChannelId())).collect(Collectors.toList());
        }

        String settlementUrl = String.format(h5SettleUrl, "", request.getPartnerSource(), request.getPartnerSourceOrderId());
        if (CollectionUtils.isEmpty(jdOrderList)) {
            log.error("InspectionSheetJsfExportImpl queryOrderInfo 查询订单信息不存在!request={}", JSON.toJSONString(request));
            queryOrderDTO.setRedirectUrl(settlementUrl);
        } else {
            log.info("InspectionSheetJsfExportImpl queryOrderInfo 查询订单信息 jdOrderList:{}", JSON.toJSONString(jdOrderList));
            jdOrder = jdOrderList.get(0);
            queryOrderDTO.setOrderId(jdOrder.getOrderId());
            queryOrderDTO.setOrderStatus(jdOrder.getOrderStatus());
            queryOrderDTO.setRedirectUrl(String.format(orderDetailUrl, jdOrder.getOrderId()));
            if (!OrderStatusEnum.isCanReceive(jdOrder.getOrderStatus())) {
                queryOrderDTO.setRedirectUrl(settlementUrl);
            }
        }

        return Response.buildSuccessResult(queryOrderDTO);
    }

    @Override
    @LogAndAlarm
    public Response<List<QueryMedicalPromiseDTO>> queryMedicalPromiseInfoBatch(QueryMedicalPromiseInfoRequest request) {
        AssertUtils.nonNull(request, "request is not null");
        AssertUtils.isNotEmpty(request.getSpecimenCodes(), "request specimenCodes is empty");
        AssertUtils.nonNull(request.getPartnerSource(), "request partnerSource is null");
        AssertUtils.collectionLength(request.getSpecimenCodes(), 50, "request specimenCodes size max equal 50");

        MedicalPromiseListRequest medicalPromiseListRequest = MedicalPromiseListRequest.builder().specimenCodeList(request.getSpecimenCodes().stream().collect(Collectors.toList())).patientDetail(Boolean.TRUE).build();
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        List<MedicalPromiseDTO> resultPromiseList = new ArrayList<>();
        Map<Long, String> partnerSourcePromiseIdMap = new HashMap<>();
        log.info("InspectionSheetJsfExportImpl queryMedicalPromiseInfoBatch 查询检测单信息 medicalPromiseDTOList:{}", JSON.toJSONString(medicalPromiseDTOList));
        if (CollectionUtils.isNotEmpty(medicalPromiseDTOList)) {
            Map<Long, List<MedicalPromiseDTO>> medicalPromiseMap = medicalPromiseDTOList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromiseId));
            List<Long> promiseIds = medicalPromiseMap.keySet().stream().collect(Collectors.toList());
            List<PromiseDto> promiseList = promiseApplication.findJdhPromiseList(PromiseRepQuery.builder().promiseIds(promiseIds).build());
            log.info("InspectionSheetJsfExportImpl queryMedicalPromiseInfoBatch 查询履约单信息 promiseList:{}", JSON.toJSONString(promiseList));
            if (CollectionUtils.isNotEmpty(promiseList)) {
                Map<Long, List<MedicalPromiseDTO>> promiseMap = promiseList.stream().collect(Collectors.toMap(promise -> Long.parseLong(promise.getSourceVoucherId()), promise -> medicalPromiseMap.getOrDefault(promise.getPromiseId(), new ArrayList<>())));
                List<Long> orderIds = promiseMap.keySet().stream().collect(Collectors.toList());
                List<JdOrder> orderList = jdOrderRepository.findOrdersByList(orderIds);
                log.info("InspectionSheetJsfExportImpl queryMedicalPromiseInfoBatch 查询订单信息 orderList:{}", JSON.toJSONString(orderList));
                if (CollectionUtils.isNotEmpty(orderList)) {
                    orderList.stream().forEach(s -> {
                        if(promiseMap.containsKey(s.getOrderId())){
                            partnerSourcePromiseIdMap.put(promiseMap.get(s.getOrderId()).get(0).getPromiseId(), s.getPartnerSourceOrderId());
                            if(request.getPartnerSource().equals(s.getPartnerSource())){
                                if(Objects.isNull(request.getSaleChannelId()) || request.getSaleChannelId().equalsIgnoreCase(JSON.parseObject(s.getExtend(), JdOrderExtendVo.class).getSaleChannelId())) {
                                    resultPromiseList.addAll(promiseMap.get(s.getOrderId()));
                                }
                            }
                        }
                    });
                }
            }
        }
        log.info("InspectionSheetJsfExportImpl queryMedicalPromiseInfoBatch 查询检测单信息 resultPromiseList:{}", JSON.toJSONString(resultPromiseList));
        List<QueryMedicalPromiseDTO> result = new ArrayList<>();
        resultPromiseList.stream().forEach(s -> {
            QueryMedicalPromiseDTO queryMedicalPromiseDTO = QueryMedicalPromiseDTO.builder()
                    .partnerSourceOrderId(partnerSourcePromiseIdMap.get(s.getPromiseId()))
                    .specimenCode(s.getSpecimenCode())
                    .patient(QueryMedicalPromisePatientDTO.builder()
                            .name(s.getName())
                            .gender(s.getGender())
                            .age(s.getAge())
                            .build())
                    .build();
            result.add(queryMedicalPromiseDTO);
        });
        return Response.buildSuccessResult(result);
    }
}